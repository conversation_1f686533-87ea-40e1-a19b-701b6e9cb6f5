/**
 * Logging utilities with colored output
 */

import chalk from 'chalk';
import { ConfigService } from '../services/config.js';

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export class Logger {
  private static instance: Logger;
  private configService: ConfigService;

  private constructor() {
    this.configService = ConfigService.getInstance();
  }

  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  /**
   * Log debug message
   */
  debug(message: string, ...args: unknown[]): void {
    this.log('debug', message, ...args);
  }

  /**
   * Log info message
   */
  info(message: string, ...args: unknown[]): void {
    this.log('info', message, ...args);
  }

  /**
   * Log warning message
   */
  warn(message: string, ...args: unknown[]): void {
    this.log('warn', message, ...args);
  }

  /**
   * Log error message
   */
  error(message: string, ...args: unknown[]): void {
    this.log('error', message, ...args);
  }

  /**
   * Log success message
   */
  success(message: string, ...args: unknown[]): void {
    console.log(chalk.green('✓'), chalk.green(message), ...args);
  }

  /**
   * Core logging method
   */
  private log(level: LogLevel, message: string, ...args: unknown[]): void {
    const config = this.configService.getConfig();
    
    if (!this.shouldLog(level, config.logLevel)) {
      return;
    }

    const timestamp = new Date().toISOString();
    const prefix = this.getPrefix(level);
    const coloredMessage = this.colorMessage(level, message);
    
    console.log(`${chalk.gray(timestamp)} ${prefix} ${coloredMessage}`, ...args);
  }

  /**
   * Check if message should be logged based on log level
   */
  private shouldLog(messageLevel: LogLevel, configLevel: LogLevel): boolean {
    const levels: Record<LogLevel, number> = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3,
    };

    return levels[messageLevel] >= levels[configLevel];
  }

  /**
   * Get colored prefix for log level
   */
  private getPrefix(level: LogLevel): string {
    switch (level) {
      case 'debug':
        return chalk.blue('DEBUG');
      case 'info':
        return chalk.cyan('INFO ');
      case 'warn':
        return chalk.yellow('WARN ');
      case 'error':
        return chalk.red('ERROR');
      default:
        return chalk.white('LOG  ');
    }
  }

  /**
   * Color message based on log level
   */
  private colorMessage(level: LogLevel, message: string): string {
    switch (level) {
      case 'debug':
        return chalk.blue(message);
      case 'info':
        return chalk.white(message);
      case 'warn':
        return chalk.yellow(message);
      case 'error':
        return chalk.red(message);
      default:
        return message;
    }
  }
}

// Export singleton instance
export const logger = Logger.getInstance();
