{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": false, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": true, "skipLibCheck": true, "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "removeComments": false, "resolveJsonModule": true, "isolatedModules": true, "verbatimModuleSyntax": false, "lib": ["ES2022", "DOM"], "types": ["node", "jest"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "tests"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}