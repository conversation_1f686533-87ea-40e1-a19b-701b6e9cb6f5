{"name": "arien-ai", "version": "1.0.0", "description": "Modern CLI terminal system powered by AI with multi-provider support", "main": "dist/index.js", "type": "module", "engines": {"node": ">=22.0.0"}, "scripts": {"dev": "tsx src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "clean": "rm -rf dist"}, "bin": {"arien": "./dist/index.js"}, "keywords": ["ai", "cli", "terminal", "openai", "deepseek", "typescript", "nodejs"], "author": "Arien AI", "license": "MIT", "dependencies": {"openai": "4.77.0", "commander": "^12.1.0", "inquirer": "^12.1.0", "chalk": "^5.3.0", "dotenv": "^16.4.7", "ora": "^8.1.1", "boxen": "^8.0.1", "figlet": "^1.8.0"}, "devDependencies": {"typescript": "^5.8.2", "@types/node": "^22.10.2", "@types/inquirer": "^9.0.7", "@types/figlet": "^1.7.0", "tsx": "^4.19.2", "eslint": "^9.17.0", "@typescript-eslint/eslint-plugin": "^8.18.2", "@typescript-eslint/parser": "^8.18.2", "prettier": "^3.4.2", "jest": "^29.7.0", "@types/jest": "^29.5.14", "ts-jest": "^29.2.5"}}