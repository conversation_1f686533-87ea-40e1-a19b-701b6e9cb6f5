/**
 * Test connection to AI providers
 */

import { AIService } from '../src/services/ai.js';
import { ConfigService } from '../src/services/config.js';
import chalk from 'chalk';

async function testConnections() {
  console.log(chalk.cyan('🔍 Testing AI Provider Connections\n'));

  const aiService = new AIService();
  const configService = ConfigService.getInstance();

  // Test available providers with their default models
  const providerConfigs = [
    { provider: 'openai', model: 'gpt-4' },
    { provider: 'deepseek', model: 'deepseek-chat' }
  ];

  for (const config of providerConfigs) {
    console.log(chalk.blue(`\n📡 Testing ${config.provider} with model ${config.model}...`));

    // Check if API key is available
    const apiKey = configService.getApiKey(config.provider);

    if (!apiKey) {
      console.log(chalk.yellow(`⚠️  No API key found for ${config.provider}`));
      console.log(chalk.gray(`   Set environment variable or configure via CLI`));
      continue;
    }

    // Check if API key is valid format
    if (apiKey === 'your_openai_api_key_here' || apiKey.length < 20) {
      console.log(chalk.yellow(`⚠️  Invalid API key format for ${config.provider}`));
      console.log(chalk.gray(`   Please configure a valid API key`));
      continue;
    }

    try {
      // Test connection
      const isConnected = await aiService.testConnection(config);

      if (isConnected) {
        console.log(chalk.green(`✅ ${config.provider} connection successful`));

        // Test a simple message
        const response = await aiService.sendMessage(
          'Hello! Please respond with just "Connection test successful"',
          'You are a helpful assistant. Keep responses brief.',
          config
        );

        console.log(chalk.cyan(`   Response: ${response.content.substring(0, 100)}...`));

        if (response.usage) {
          console.log(chalk.gray(`   Tokens used: ${response.usage.totalTokens}`));
        }
      }
    } catch (error) {
      console.log(chalk.red(`❌ ${config.provider} connection failed`));

      if (error instanceof Error) {
        console.log(chalk.red(`   Error: ${error.message}`));

        // Provide suggestions
        if (error.message.includes('API key')) {
          console.log(chalk.yellow('   💡 Check your API key configuration'));
        } else if (error.message.includes('connection')) {
          console.log(chalk.yellow('   💡 Check your internet connection'));
        }
      }
    }
  }

  console.log(chalk.cyan('\n🏁 Connection tests completed'));
}

// Run the test
testConnections().catch(error => {
  console.error(chalk.red('Test failed:'), error);
  process.exit(1);
});
