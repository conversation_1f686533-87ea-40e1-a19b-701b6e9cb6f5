# Arien AI CLI

A modern CLI terminal system powered by AI with multi-provider support. Built with TypeScript 5.8, Node.js 22, and the latest OpenAI SDK (4.77.0).

## Features

- 🤖 **Multi-Provider AI Support**: OpenAI, DeepSeek, and any OpenAI-compatible provider
- 🚀 **Modern TypeScript**: Built with TypeScript 5.8 and strict type checking
- 🎨 **Beautiful CLI**: Colorful, interactive terminal interface with spinners and boxes
- ⚙️ **Flexible Configuration**: Environment variables, config files, and command-line options
- 🔧 **Extensible Architecture**: Easy to add new providers and commands
- 📝 **Structured Logging**: Configurable log levels with colored output

## Supported Providers

### OpenAI
- **Models**: GPT-4, GPT-4 Turbo, GPT-3.5 Turbo
- **Base URL**: `https://api.openai.com/v1`
- **API Key**: Set `OPENAI_API_KEY` environment variable

### DeepSeek
- **Models**: `deepseek-chat`, `deepseek-reasoner`
- **Base URL**: `https://api.deepseek.com`
- **API Key**: Set `DEEPSEEK_API_KEY` environment variable

## Installation

### Prerequisites
- Node.js 22 or higher
- npm, yarn, or pnpm

### Install Dependencies

```bash
npm install
```

### Setup Environment Variables

1. Copy the example environment file:
```bash
cp .env.example .env
```

2. Edit `.env` and add your API keys:
```bash
OPENAI_API_KEY=your_openai_api_key_here
DEEPSEEK_API_KEY=your_deepseek_api_key_here
```

## Usage

### Development Mode

```bash
npm run dev
```

### Build and Run

```bash
npm run build
npm start
```

### Commands

#### Start Chat Session
```bash
# Interactive chat with default provider
npm run dev chat

# Specify provider and model
npm run dev chat --provider deepseek --model deepseek-chat

# Set temperature and max tokens
npm run dev chat --temperature 0.8 --max-tokens 1000

# Use system prompt
npm run dev chat --system "You are a helpful coding assistant"

# Enable verbose logging
npm run dev chat --verbose

# Force provider selection (even if default is set)
npm run dev chat --select-provider

# Force creation of new session (skip saved configurations)
npm run dev chat --new-session
```

#### View Available Providers
```bash
npm run dev providers
```

#### Manage Configuration
```bash
# Show current configuration
npm run dev config --show

# Reset to defaults
npm run dev config --reset

# Set default provider
npm run dev config --set-provider deepseek

# Set default model
npm run dev config --set-model deepseek-reasoner

# Configure API key interactively
npm run dev config --configure-api-key openai
npm run dev config --configure-api-key deepseek

# Clear all saved sessions
npm run dev config --clear-sessions
```

## Saved Sessions

The CLI automatically saves your session configurations for easy reuse:

### Automatic Session Management
- **Auto-save**: When you configure a new session, you'll be asked if you want to save it
- **Smart Selection**: On startup, choose from saved sessions or create new ones
- **Last Used**: Quickly continue with your most recent session
- **Session History**: View and manage all saved sessions

### Session Features
- **Provider & Model**: Remembers your AI provider and model choices
- **Custom Settings**: Saves temperature, max tokens, and system prompts
- **Quick Access**: Resume any saved session with one selection
- **Auto-cleanup**: Sessions are sorted by last used date

## Configuration

The CLI supports multiple configuration methods:

### 1. Environment Variables
```bash
OPENAI_API_KEY=your_key
DEEPSEEK_API_KEY=your_key
ARIEN_DEFAULT_PROVIDER=openai
ARIEN_DEFAULT_MODEL=gpt-4
ARIEN_LOG_LEVEL=info
```

### 2. Configuration File
Located at `~/.arien-ai/config.json`:
```json
{
  "defaultProvider": "openai",
  "defaultModel": "gpt-4",
  "temperature": 0.7,
  "maxTokens": 2048,
  "logLevel": "info",
  "providers": {
    "openai": {
      "provider": "openai",
      "model": "gpt-4",
      "apiKey": "your_key",
      "temperature": 0.7,
      "maxTokens": 2048
    }
  }
}
```

### 3. Command Line Options
```bash
npm run dev chat --provider openai --model gpt-4 --temperature 0.8
```

## Architecture

```
src/
├── commands/          # CLI command implementations
├── providers/         # AI provider configurations
├── services/          # Core services (AI, config)
├── utils/             # Utility functions
├── types/             # TypeScript type definitions
└── index.ts           # Main entry point
```

## Adding New Providers

To add a new OpenAI-compatible provider:

1. Add provider configuration to `src/types/index.ts`:
```typescript
export const AI_PROVIDERS: Record<string, AIProvider> = {
  // ... existing providers
  newprovider: {
    name: 'New Provider',
    baseURL: 'https://api.newprovider.com/v1',
    models: ['model-1', 'model-2'],
    apiKeyEnvVar: 'NEWPROVIDER_API_KEY',
    description: 'New AI provider'
  }
};
```

2. The provider will automatically be available in the CLI!

## Development

### Scripts
- `npm run dev` - Run in development mode with tsx
- `npm run build` - Build TypeScript to JavaScript
- `npm run start` - Run built JavaScript
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier
- `npm run test` - Run tests

### Tech Stack
- **TypeScript 5.8** - Latest TypeScript with strict type checking
- **Node.js 22** - Latest LTS Node.js version
- **OpenAI SDK 4.77.0** - Official OpenAI TypeScript library
- **Commander.js** - CLI framework
- **Inquirer.js** - Interactive prompts
- **Chalk** - Terminal colors
- **Ora** - Loading spinners
- **Boxen** - Terminal boxes

## License

MIT License - see LICENSE file for details.
