/**
 * AI Provider configurations and factory
 */

import OpenAI from 'openai';
import { AI_PROVIDERS, type AIProvider, type AIProviderConfig } from '../types/index.js';

export class ProviderFactory {
  private static instances = new Map<string, OpenAI>();

  /**
   * Get or create an OpenAI client instance for the specified provider
   */
  static getClient(config: AIProviderConfig): OpenAI {
    const cacheKey = `${config.provider}-${config.baseURL || ''}`;

    if (this.instances.has(cacheKey)) {
      return this.instances.get(cacheKey)!;
    }

    const provider = AI_PROVIDERS[config.provider];
    if (!provider) {
      throw new Error(`Unknown provider: ${config.provider}`);
    }

    // Enhanced client configuration with proper timeouts and retries
    const client = new OpenAI({
      apiKey: config.apiKey,
      baseURL: config.baseURL || provider.baseURL,
      timeout: 60000, // 60 seconds timeout
      maxRetries: 3, // Retry failed requests up to 3 times
      // Add custom fetch with better error handling
      fetch: async (url: RequestInfo | URL, init?: RequestInit) => {
        try {
          const response = await fetch(url, {
            ...init,
            signal: AbortSignal.timeout(60000), // 60 second timeout
          });
          return response;
        } catch (error) {
          if (error instanceof Error) {
            // Enhance error messages for better debugging
            if (error.name === 'AbortError' || error.message.includes('timeout')) {
              throw new Error(`Connection timeout to ${provider.name}. Please check your internet connection and try again.`);
            }
            if (error.message.includes('ENOTFOUND') || error.message.includes('ECONNREFUSED')) {
              throw new Error(`Cannot connect to ${provider.name} API. Please check your internet connection and try again.`);
            }
            if (error.message.includes('401') || error.message.includes('Unauthorized')) {
              throw new Error(`Invalid API key for ${provider.name}. Please check your API key and try again.`);
            }
          }
          throw error;
        }
      },
    });

    this.instances.set(cacheKey, client);
    return client;
  }

  /**
   * Get available providers
   */
  static getAvailableProviders(): AIProvider[] {
    return Object.values(AI_PROVIDERS);
  }

  /**
   * Get provider by name
   */
  static getProvider(name: string): AIProvider | undefined {
    return AI_PROVIDERS[name];
  }

  /**
   * Validate provider configuration
   */
  static validateConfig(config: AIProviderConfig): void {
    const provider = this.getProvider(config.provider);
    if (!provider) {
      throw new Error(`Unknown provider: ${config.provider}`);
    }

    if (!config.apiKey) {
      throw new Error(`API key is required for provider: ${config.provider}`);
    }

    if (!provider.models.includes(config.model)) {
      throw new Error(
        `Model ${config.model} is not supported by provider ${config.provider}. ` +
        `Supported models: ${provider.models.join(', ')}`
      );
    }

    // Validate API key format
    this.validateApiKey(config.provider, config.apiKey);
  }

  /**
   * Validate API key format for specific providers
   */
  static validateApiKey(provider: string, apiKey: string): void {
    if (!apiKey || apiKey.trim().length === 0) {
      throw new Error(`API key cannot be empty for provider: ${provider}`);
    }

    // Basic API key format validation
    const trimmedKey = apiKey.trim();

    switch (provider) {
      case 'openai':
        if (!trimmedKey.startsWith('sk-') || trimmedKey.length < 20) {
          throw new Error('OpenAI API key should start with "sk-" and be at least 20 characters long');
        }
        break;
      case 'deepseek':
        if (!trimmedKey.startsWith('sk-') || trimmedKey.length < 20) {
          throw new Error('DeepSeek API key should start with "sk-" and be at least 20 characters long');
        }
        break;
      default:
        if (trimmedKey.length < 10) {
          throw new Error(`API key for ${provider} seems too short (minimum 10 characters)`);
        }
    }
  }

  /**
   * Test connection to provider API
   */
  static async testConnection(config: AIProviderConfig): Promise<boolean> {
    try {
      this.validateConfig(config);
      const client = this.getClient(config);

      // Test with a minimal request
      await client.models.list();
      return true;
    } catch (error) {
      if (error instanceof Error) {
        if (error.message.includes('401') || error.message.includes('Unauthorized')) {
          throw new Error(`Invalid API key for ${config.provider}. Please check your API key.`);
        }
        if (error.message.includes('timeout') || error.message.includes('ENOTFOUND')) {
          throw new Error(`Cannot connect to ${config.provider} API. Please check your internet connection.`);
        }
      }
      throw error;
    }
  }

  /**
   * Clear cached instances (useful for testing)
   */
  static clearCache(): void {
    this.instances.clear();
  }
}

/**
 * Default configurations for each provider
 */
export const DEFAULT_PROVIDER_CONFIGS: Record<string, Partial<AIProviderConfig>> = {
  openai: {
    provider: 'openai',
    model: 'gpt-4',
    temperature: 0.7,
    maxTokens: 2048,
  },
  deepseek: {
    provider: 'deepseek',
    model: 'deepseek-chat',
    temperature: 0.7,
    maxTokens: 2048,
  },
};

/**
 * Get default configuration for a provider
 */
export function getDefaultConfig(providerName: string): Partial<AIProviderConfig> {
  return DEFAULT_PROVIDER_CONFIGS[providerName] || {};
}
