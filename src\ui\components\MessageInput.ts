/**
 * Modern Message Input Component
 * A sophisticated input component with enhanced styling and features for the chat interface
 */

import chalk from 'chalk';
import inquirer from 'inquirer';
import { stdout } from 'process';
import type { MessageInputConfig, MessageInputResult, InputHistoryItem, SpinnerState } from '../../types/index.js';

export class MessageInput {
  private config: Required<MessageInputConfig>;
  private history: InputHistoryItem[];
  private static instance: MessageInput | null = null;
  private spinnerState: SpinnerState | null = null;
  private spinnerInterval: NodeJS.Timeout | null = null;

  constructor(config: MessageInputConfig = {}) {
    this.config = {
      placeholder: config.placeholder || 'Type your message...',
      multiline: config.multiline ?? false, // Keep simple for now
      maxLines: config.maxLines || 5,
      showCharCount: config.showCharCount ?? false,
      showWordCount: config.showWordCount ?? false,
      enableHistory: config.enableHistory ?? true,
      enableAutoComplete: config.enableAutoComplete ?? false, // Keep simple for now
      theme: {
        borderColor: 'cyan',
        focusColor: 'blue',
        textColor: 'white',
        placeholderColor: 'gray',
        counterColor: 'yellow',
        errorColor: 'red',
        successColor: 'green',
        spinnerColor: 'cyan',
        timerColor: 'yellow',
        ...config.theme,
      },
    };

    this.history = [];
  }

  /**
   * Get singleton instance
   */
  static getInstance(config?: MessageInputConfig): MessageInput {
    if (!MessageInput.instance) {
      MessageInput.instance = new MessageInput(config);
    }
    return MessageInput.instance;
  }

  /**
   * Get terminal width with fallback
   */
  private getTerminalWidth(): number {
    return stdout.columns || process.stdout.columns || 80;
  }

  /**
   * Display the modern input prompt and wait for user input
   */
  async prompt(message: string = 'You:'): Promise<MessageInputResult> {
    // Display the modern input header
    this.displayInputHeader(message);

    try {
      const { userInput } = await inquirer.prompt([
        {
          type: 'input',
          name: 'userInput',
          message: this.createStyledPrompt(),
          validate: (input: string) => {
            const trimmed = input.trim();
            if (trimmed.length === 0) {
              return chalk.hex(this.config.theme.errorColor!)('Please enter a message');
            }
            return true;
          },
          transformer: (input: string, { isFinal }: { isFinal: boolean }) => {
            if (!isFinal && input.length === 0) {
              return chalk.hex(this.config.theme.placeholderColor!)(this.config.placeholder);
            }
            return chalk.hex(this.config.theme.textColor!)(input);
          },
        },
      ]);

      const trimmedInput = userInput.trim();

      // Handle special commands
      if (trimmedInput.toLowerCase() === 'exit') {
        this.displayInputFooter();
        return { value: trimmedInput, command: 'exit' };
      }

      if (trimmedInput.toLowerCase() === 'clear') {
        this.displayInputFooter();
        return { value: trimmedInput, command: 'clear' };
      }

      // Add to history
      if (this.config.enableHistory) {
        this.addToHistory(trimmedInput);
      }

      this.displayInputFooter();
      return { value: trimmedInput };

    } catch (error) {
      this.displayInputFooter();
      return { value: '', cancelled: true };
    }
  }

  /**
   * Display the modern input header with styling
   */
  private displayInputHeader(message: string): void {
    // Remove the box display - just keep the method for compatibility
    // but don't display anything
  }

  /**
   * Create styled prompt message
   */
  private createStyledPrompt(): string {
    return '';
  }

  /**
   * Display the input footer
   */
  private displayInputFooter(): void {
    // Remove the footer display - just keep the method for compatibility
    // but don't display anything
  }

  /**
   * Get spinner animation frames (modern ball animation)
   */
  private getSpinnerFrames(): string[] {
    return ['●', '○', '◐', '◓', '◑', '◒'];
  }

  /**
   * Start the integrated spinner animation
   */
  startSpinner(message: string = 'Processing'): void {
    if (this.spinnerState) {
      this.stopSpinner();
    }

    this.spinnerState = {
      isActive: true,
      startTime: new Date(),
      frameIndex: 0,
      message,
    };

    this.spinnerInterval = setInterval(() => {
      this.updateSpinnerDisplay();
    }, 150); // Update every 150ms for smooth animation

    // Initial display
    this.updateSpinnerDisplay();
  }

  /**
   * Stop the spinner animation
   */
  stopSpinner(): void {
    if (this.spinnerInterval) {
      clearInterval(this.spinnerInterval);
      this.spinnerInterval = null;
    }

    if (this.spinnerState) {
      // Clear the spinner line
      process.stdout.write('\r\x1b[K');
      this.spinnerState = null;
    }
  }

  /**
   * Update the spinner display with animation and elapsed time
   */
  private updateSpinnerDisplay(): void {
    if (!this.spinnerState) return;

    const theme = this.config.theme;
    const terminalWidth = this.getTerminalWidth();
    const frames = this.getSpinnerFrames();
    const currentFrame = frames[this.spinnerState.frameIndex % frames.length];

    // Calculate elapsed time
    const elapsed = Math.floor((Date.now() - this.spinnerState.startTime.getTime()) / 1000);
    const elapsedText = `${elapsed}s`;

    // Create spinner line
    const spinnerText = `${currentFrame} ${this.spinnerState.message}`;
    const contentWidth = terminalWidth - 2; // Account for borders
    const availableSpace = contentWidth - spinnerText.length - elapsedText.length - 2; // 2 for spacing
    const padding = Math.max(0, availableSpace);

    const spinnerLine = chalk.hex(theme.borderColor!)('│ ') +
                       chalk.hex(theme.spinnerColor!)(currentFrame) + ' ' +
                       chalk.hex(theme.textColor!)(this.spinnerState.message) +
                       ' '.repeat(padding) +
                       chalk.hex(theme.timerColor!)(elapsedText) +
                       chalk.hex(theme.borderColor!)(' │');

    // Update display
    process.stdout.write('\r' + spinnerLine);

    // Increment frame index
    this.spinnerState.frameIndex++;
  }

  /**
   * Add input to history
   */
  private addToHistory(value: string): void {
    // Don't add empty values or duplicates
    if (value.trim().length === 0) return;

    const lastItem = this.history[this.history.length - 1];
    if (lastItem && lastItem.value === value) return;

    this.history.push({
      value,
      timestamp: new Date(),
    });

    // Keep history size manageable (last 100 items)
    if (this.history.length > 100) {
      this.history = this.history.slice(-100);
    }
  }

  /**
   * Show error message with modern styling
   */
  showError(message: string): void {
    const theme = this.config.theme;
    const errorLine = chalk.hex(theme.borderColor!)('│ ') +
                     chalk.hex(theme.errorColor!)(`❌ ${message}`) +
                     chalk.hex(theme.borderColor!)(' '.repeat(Math.max(0, 54 - message.length)) + '│');
    console.log(errorLine);
  }

  /**
   * Show success message with modern styling
   */
  showSuccess(message: string): void {
    const theme = this.config.theme;
    const successLine = chalk.hex(theme.borderColor!)('│ ') +
                       chalk.hex(theme.successColor!)(`Success: ${message}`) +
                       chalk.hex(theme.borderColor!)(' '.repeat(Math.max(0, 54 - message.length)) + '│');
    console.log(successLine);
  }

  /**
   * Get input history
   */
  getHistory(): InputHistoryItem[] {
    return [...this.history];
  }

  /**
   * Clear input history
   */
  clearHistory(): void {
    this.history = [];
  }

  /**
   * Set custom theme
   */
  setTheme(theme: Partial<MessageInputConfig['theme']>): void {
    this.config.theme = { ...this.config.theme, ...theme };
  }

  /**
   * Get the last N history items
   */
  getRecentHistory(count: number = 10): InputHistoryItem[] {
    return this.history.slice(-count);
  }

  /**
   * Check if history is enabled
   */
  isHistoryEnabled(): boolean {
    return this.config.enableHistory;
  }

  /**
   * Get current configuration
   */
  getConfig(): Required<MessageInputConfig> {
    return { ...this.config };
  }
}
