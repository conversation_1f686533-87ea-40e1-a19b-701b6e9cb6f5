/**
 * Simple Message Input Component
 * A clean, minimal input component for the chat interface
 */

import chalk from 'chalk';
import inquirer from 'inquirer';
import { stdout } from 'process';
import type { MessageInputConfig, MessageInputResult, InputHistoryItem } from '../../types/index.js';

export class MessageInput {
  private config: Required<MessageInputConfig>;
  private history: InputHistoryItem[];
  private static instance: MessageInput | null = null;

  constructor(config: MessageInputConfig = {}) {
    this.config = {
      placeholder: config.placeholder || 'Type your message...',
      multiline: config.multiline ?? false,
      maxLines: config.maxLines || 5,
      showCharCount: config.showCharCount ?? false,
      showWordCount: config.showWordCount ?? false,
      enableHistory: config.enableHistory ?? true,
      enableAutoComplete: config.enableAutoComplete ?? false,
      theme: {
        borderColor: '#6B7280', // Gray
        focusColor: '#3B82F6', // Blue
        textColor: '#FFFFFF', // White
        placeholderColor: '#9CA3AF', // Light gray
        counterColor: '#F59E0B', // Amber
        errorColor: '#EF4444', // Red
        successColor: '#10B981', // Green
        spinnerColor: '#06B6D4', // Cyan
        timerColor: '#F59E0B', // Amber
        ...config.theme,
      },
    };

    this.history = [];
  }

  /**
   * Get singleton instance
   */
  static getInstance(config?: MessageInputConfig): MessageInput {
    if (!MessageInput.instance) {
      MessageInput.instance = new MessageInput(config);
    }
    return MessageInput.instance;
  }

  /**
   * Get terminal width with fallback
   */
  private getTerminalWidth(): number {
    return stdout.columns || process.stdout.columns || 80;
  }

  /**
   * Display simple input prompt and wait for user input
   */
  async prompt(): Promise<MessageInputResult> {
    try {
      const { userInput } = await inquirer.prompt({
        type: 'input',
        name: 'userInput',
        message: '',
        validate: (input: string) => {
          const trimmed = input.trim();
          if (trimmed.length === 0) {
            return 'Please enter a message';
          }
          if (trimmed.length > 4000) {
            return 'Message too long (max 4000 characters)';
          }
          return true;
        },
      });

      const trimmedInput = userInput.trim();

      // Handle special commands
      if (trimmedInput.toLowerCase() === 'exit') {
        return { value: trimmedInput, command: 'exit' };
      }

      if (trimmedInput.toLowerCase() === 'clear') {
        return { value: trimmedInput, command: 'clear' };
      }

      // Add to history
      if (this.config.enableHistory) {
        this.addToHistory(trimmedInput);
      }

      return { value: trimmedInput };

    } catch (error) {
      return { value: '', cancelled: true };
    }
  }

  /**
   * Display user message
   */
  displayUserMessage(message: string): void {
    console.log(chalk.white('user'));
    console.log(message);
    console.log();
  }

  /**
   * Display AI message
   */
  displayAIMessage(message: string): void {
    console.log(chalk.cyan('ARIEN'));
    console.log(message);
    console.log();
  }



  /**
   * Add input to history
   */
  private addToHistory(value: string): void {
    // Don't add empty values or duplicates
    if (value.trim().length === 0) return;

    const lastItem = this.history[this.history.length - 1];
    if (lastItem && lastItem.value === value) return;

    this.history.push({
      value,
      timestamp: new Date(),
    });

    // Keep history size manageable (last 100 items)
    if (this.history.length > 100) {
      this.history = this.history.slice(-100);
    }
  }

  /**
   * Show error message
   */
  showError(message: string): void {
    console.log(chalk.red(`Error: ${message}`));
    console.log();
  }

  /**
   * Show success message
   */
  showSuccess(message: string): void {
    console.log(chalk.green(`✓ ${message}`));
    console.log();
  }

  /**
   * Get input history
   */
  getHistory(): InputHistoryItem[] {
    return [...this.history];
  }

  /**
   * Clear input history
   */
  clearHistory(): void {
    this.history = [];
  }

  /**
   * Set custom theme
   */
  setTheme(theme: Partial<MessageInputConfig['theme']>): void {
    this.config.theme = { ...this.config.theme, ...theme };
  }

  /**
   * Get the last N history items
   */
  getRecentHistory(count: number = 10): InputHistoryItem[] {
    return this.history.slice(-count);
  }

  /**
   * Check if history is enabled
   */
  isHistoryEnabled(): boolean {
    return this.config.enableHistory;
  }

  /**
   * Get current configuration
   */
  getConfig(): Required<MessageInputConfig> {
    return { ...this.config };
  }
}
