/**
 * Core type definitions for Arien AI CLI
 */

export interface AIProvider {
  name: string;
  baseURL: string;
  models: string[];
  apiKeyEnvVar: string;
  description: string;
}

export interface AIProviderConfig {
  provider: string;
  model: string;
  apiKey: string;
  baseURL?: string | undefined;
  temperature?: number | undefined;
  maxTokens?: number | undefined;
}

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
  timestamp?: Date;
}

export interface ChatSession {
  id: string;
  messages: ChatMessage[];
  provider: string;
  model: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface SavedChatSession {
  id: string;
  name: string;
  provider: string;
  model: string;
  temperature?: number | undefined;
  maxTokens?: number | undefined;
  systemPrompt?: string | undefined;
  lastUsed: Date;
  createdAt: Date;
}

export interface CLIConfig {
  defaultProvider: string;
  defaultModel: string;
  temperature: number;
  maxTokens: number;
  providers: Record<string, AIProviderConfig>;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  lastUsedSession?: SavedChatSession | undefined;
  savedSessions: Record<string, SavedChatSession>;
}

export interface CommandOptions {
  provider?: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
  verbose?: boolean;
  config?: string;
}

export interface AIResponse {
  content: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  } | undefined;
  model: string;
  provider: string;
}

export interface ProviderError extends Error {
  provider: string;
  statusCode?: number;
  details?: unknown;
}

// Predefined AI providers
export const AI_PROVIDERS: Record<string, AIProvider> = {
  openai: {
    name: 'OpenAI',
    baseURL: 'https://api.openai.com/v1',
    models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
    apiKeyEnvVar: 'OPENAI_API_KEY',
    description: 'OpenAI GPT models'
  },
  deepseek: {
    name: 'DeepSeek',
    baseURL: 'https://api.deepseek.com/v1',
    models: ['deepseek-chat', 'deepseek-reasoner'],
    apiKeyEnvVar: 'DEEPSEEK_API_KEY',
    description: 'DeepSeek AI models'
  }
};

export type ProviderName = keyof typeof AI_PROVIDERS;

// UI Component Types
export interface MessageInputConfig {
  placeholder?: string;
  multiline?: boolean;
  maxLines?: number;
  showCharCount?: boolean;
  showWordCount?: boolean;
  enableHistory?: boolean;
  enableAutoComplete?: boolean;
  theme?: MessageInputTheme;
}

export interface MessageInputTheme {
  borderColor?: string;
  focusColor?: string;
  textColor?: string;
  placeholderColor?: string;
  counterColor?: string;
  errorColor?: string;
  successColor?: string;
  spinnerColor?: string;
  timerColor?: string;
}

export interface MessageInputResult {
  value: string;
  command?: string;
  cancelled?: boolean;
}

export interface InputHistoryItem {
  value: string;
  timestamp: Date;
}

export interface MessageInputState {
  value: string;
  cursorPosition: number;
  historyIndex: number;
  isMultiline: boolean;
  lines: string[];
  currentLine: number;
  isProcessing: boolean;
  processingStartTime?: Date;
}

export interface SpinnerState {
  isActive: boolean;
  startTime: Date;
  frameIndex: number;
  message: string;
}

export interface SessionInfo {
  sessionId: string;
  workdir: string;
  model: string;
  provider: string;
  approval: string;
  contextLeft: number;
}

export interface StatusBarState {
  isVisible: boolean;
  message: string;
  controls: string;
  contextInfo: string;
  spinner?: SpinnerState;
}
