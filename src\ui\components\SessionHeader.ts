/**
 * Session Header Component
 * Displays session information in a modern boxed format
 */

import chalk from 'chalk';
import { stdout } from 'process';
import type { SessionInfo } from '../../types/index.js';

export class SessionHeader {
  private static instance: SessionHeader | null = null;

  private constructor() {}

  /**
   * Get singleton instance
   */
  static getInstance(): SessionHeader {
    if (!SessionHeader.instance) {
      SessionHeader.instance = new SessionHeader();
    }
    return SessionHeader.instance;
  }

  /**
   * Get terminal width with fallback
   */
  private getTerminalWidth(): number {
    return stdout.columns || process.stdout.columns || 80;
  }

  /**
   * Display the session header
   */
  display(sessionInfo: SessionInfo): void {
    const terminalWidth = this.getTerminalWidth();
    const boxWidth = Math.min(terminalWidth - 4, 120); // Max width of 120, with margin

    console.log(); // Add spacing before header

    // Create top border
    const topBorder = this.createBorderLine('top', boxWidth);
    console.log(topBorder);

    // Create session info lines
    const sessionLine = this.createContentLine(`localhost session: ${sessionInfo.sessionId}`, boxWidth);
    const workdirLine = this.createContentLine(`↳ workdir: ${sessionInfo.workdir}`, boxWidth);
    const modelLine = this.createContentLine(`↳ model: ${sessionInfo.model}`, boxWidth);
    const providerLine = this.createContentLine(`↳ provider: ${sessionInfo.provider}`, boxWidth);
    const approvalLine = this.createContentLine(`↳ approval: ${sessionInfo.approval}`, boxWidth);

    console.log(sessionLine);
    console.log(workdirLine);
    console.log(modelLine);
    console.log(providerLine);
    console.log(approvalLine);

    // Create bottom border
    const bottomBorder = this.createBorderLine('bottom', boxWidth);
    console.log(bottomBorder);
    console.log(); // Add spacing after header
  }

  /**
   * Create a styled border line
   */
  private createBorderLine(type: 'top' | 'bottom', width: number): string {
    const borderChar = '─';
    const leftChar = type === 'top' ? '╭' : '╰';
    const rightChar = type === 'top' ? '╮' : '╯';

    return chalk.gray(leftChar + borderChar.repeat(width - 2) + rightChar);
  }

  /**
   * Create a styled content line
   */
  private createContentLine(content: string, width: number): string {
    const maxContentWidth = width - 4; // Account for borders and padding
    let paddedContent = content;

    // Truncate if too long
    if (content.length > maxContentWidth) {
      paddedContent = content.substring(0, maxContentWidth - 3) + '...';
    }

    // Pad to full width
    const padding = maxContentWidth - paddedContent.length;
    paddedContent = paddedContent + ' '.repeat(Math.max(0, padding));

    return chalk.gray('│ ') + chalk.white(paddedContent) + chalk.gray(' │');
  }

  /**
   * Clear the session header (for screen clearing)
   */
  clear(): void {
    // This method can be used to clear the header if needed
    console.clear();
  }
}
