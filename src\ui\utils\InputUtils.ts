/**
 * Utility functions for input components
 */

import chalk from 'chalk';

export class InputUtils {
  /**
   * Create a styled border line
   */
  static createBorderLine(
    type: 'top' | 'middle' | 'bottom' | 'separator',
    width: number = 60,
    color: string = 'cyan',
    title?: string
  ): string {
    const borderChar = '─';
    let leftChar: string;
    let rightChar: string;

    switch (type) {
      case 'top':
        leftChar = '┌';
        rightChar = '┐';
        break;
      case 'middle':
        leftChar = '├';
        rightChar = '┤';
        break;
      case 'bottom':
        leftChar = '└';
        rightChar = '┘';
        break;
      case 'separator':
        leftChar = '├';
        rightChar = '┤';
        break;
    }

    if (title && type === 'top') {
      const titleLength = title.length + 4; // Add padding
      const remainingWidth = Math.max(0, width - titleLength - 2);
      const leftPadding = Math.floor(remainingWidth / 2);
      const rightPadding = remainingWidth - leftPadding;
      
      return chalk.hex(color)(
        leftChar + 
        borderChar.repeat(leftPadding) + 
        ` ${title} ` + 
        borderChar.repeat(rightPadding) + 
        rightChar
      );
    }

    return chalk.hex(color)(leftChar + borderChar.repeat(width - 2) + rightChar);
  }

  /**
   * Create a styled content line
   */
  static createContentLine(
    content: string,
    width: number = 60,
    borderColor: string = 'cyan',
    contentColor?: string,
    alignment: 'left' | 'center' | 'right' = 'left'
  ): string {
    const maxContentWidth = width - 4; // Account for borders and padding
    let paddedContent = content;

    // Truncate if too long
    if (content.length > maxContentWidth) {
      paddedContent = content.substring(0, maxContentWidth - 3) + '...';
    }

    // Apply alignment
    const padding = maxContentWidth - paddedContent.length;
    switch (alignment) {
      case 'center':
        const leftPad = Math.floor(padding / 2);
        const rightPad = padding - leftPad;
        paddedContent = ' '.repeat(leftPad) + paddedContent + ' '.repeat(rightPad);
        break;
      case 'right':
        paddedContent = ' '.repeat(padding) + paddedContent;
        break;
      case 'left':
      default:
        paddedContent = paddedContent + ' '.repeat(padding);
        break;
    }

    const styledContent = contentColor 
      ? chalk.hex(contentColor)(paddedContent)
      : paddedContent;

    return chalk.hex(borderColor)('│ ') + styledContent + chalk.hex(borderColor)(' │');
  }

  /**
   * Validate input based on common patterns
   */
  static validateInput(input: string, options: {
    minLength?: number;
    maxLength?: number;
    required?: boolean;
    pattern?: RegExp;
    customValidator?: (input: string) => boolean | string;
  } = {}): boolean | string {
    const {
      minLength = 0,
      maxLength = Infinity,
      required = true,
      pattern,
      customValidator
    } = options;

    const trimmed = input.trim();

    // Check if required
    if (required && trimmed.length === 0) {
      return 'This field is required';
    }

    // Check minimum length
    if (trimmed.length < minLength) {
      return `Minimum length is ${minLength} characters`;
    }

    // Check maximum length
    if (trimmed.length > maxLength) {
      return `Maximum length is ${maxLength} characters`;
    }

    // Check pattern
    if (pattern && !pattern.test(trimmed)) {
      return 'Input format is invalid';
    }

    // Custom validation
    if (customValidator) {
      const result = customValidator(trimmed);
      if (result !== true) {
        return typeof result === 'string' ? result : 'Input is invalid';
      }
    }

    return true;
  }

  /**
   * Format text with word wrapping
   */
  static wrapText(text: string, maxWidth: number): string[] {
    const words = text.split(' ');
    const lines: string[] = [];
    let currentLine = '';

    for (const word of words) {
      if ((currentLine + word).length <= maxWidth) {
        currentLine += (currentLine ? ' ' : '') + word;
      } else {
        if (currentLine) {
          lines.push(currentLine);
        }
        currentLine = word;
      }
    }

    if (currentLine) {
      lines.push(currentLine);
    }

    return lines;
  }

  /**
   * Create a progress indicator
   */
  static createProgressIndicator(
    current: number,
    total: number,
    width: number = 20,
    color: string = 'green'
  ): string {
    const percentage = Math.min(100, Math.max(0, (current / total) * 100));
    const filledWidth = Math.floor((percentage / 100) * width);
    const emptyWidth = width - filledWidth;

    const filled = '█'.repeat(filledWidth);
    const empty = '░'.repeat(emptyWidth);

    return chalk.hex(color)(filled) + chalk.gray(empty) + ` ${percentage.toFixed(1)}%`;
  }

  /**
   * Create a spinner animation frame
   */
  static getSpinnerFrame(frameIndex: number): string {
    const frames = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'];
    return frames[frameIndex % frames.length];
  }

  /**
   * Format file size
   */
  static formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  /**
   * Format duration
   */
  static formatDuration(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  /**
   * Escape special characters for display
   */
  static escapeForDisplay(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }

  /**
   * Truncate text with ellipsis
   */
  static truncate(text: string, maxLength: number, ellipsis: string = '...'): string {
    if (text.length <= maxLength) {
      return text;
    }
    return text.substring(0, maxLength - ellipsis.length) + ellipsis;
  }
}
