#!/usr/bin/env npx tsx

/**
 * Simple example of using the Arien AI service programmatically
 */

import { config } from 'dotenv';
import { AIService } from '../src/services/ai.js';
import { ConfigService } from '../src/services/config.js';

// Load environment variables
config();

async function main(): Promise<void> {
  try {
    // Initialize services
    const aiService = new AIService();
    const configService = ConfigService.getInstance();

    console.log('🤖 Arien AI Simple Chat Example\n');

    // Check if API keys are available
    const openaiKey = configService.getApiKey('openai');
    const deepseekKey = configService.getApiKey('deepseek');

    if (!openaiKey && !deepseekKey) {
      console.error('❌ No API keys found. Please set OPENAI_API_KEY or DEEPSEEK_API_KEY environment variables.');
      process.exit(1);
    }

    // Determine which provider to use
    const provider = openaiKey ? 'openai' : 'deepseek';
    const model = provider === 'openai' ? 'gpt-4' : 'deepseek-chat';

    console.log(`📡 Using provider: ${provider} with model: ${model}\n`);

    // Send a simple message
    const response = await aiService.sendMessage(
      'Hello! Can you explain what TypeScript is in one sentence?',
      'You are a helpful programming assistant.',
      { provider, model }
    );

    console.log('🤖 AI Response:');
    console.log(response.content);
    console.log();

    if (response.usage) {
      console.log(`📊 Usage: ${response.usage.totalTokens} tokens (prompt: ${response.usage.promptTokens}, completion: ${response.usage.completionTokens})`);
    }

    console.log(`✅ Response from ${response.provider}/${response.model}`);

  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

// Run the example
main().catch(console.error);
