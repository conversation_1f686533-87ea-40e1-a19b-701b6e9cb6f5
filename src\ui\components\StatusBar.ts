/**
 * Status Bar Component
 * Displays status information at the bottom of the terminal
 */

import chalk from 'chalk';
import { stdout } from 'process';
import type { StatusBarState, SpinnerState } from '../../types/index.js';

export class StatusBar {
  private static instance: StatusBar | null = null;
  private currentState: StatusBarState | null = null;
  private spinnerInterval: NodeJS.Timeout | null = null;

  private constructor() {}

  /**
   * Get singleton instance
   */
  static getInstance(): StatusBar {
    if (!StatusBar.instance) {
      StatusBar.instance = new StatusBar();
    }
    return StatusBar.instance;
  }

  /**
   * Get terminal width with fallback
   */
  private getTerminalWidth(): number {
    return stdout.columns || process.stdout.columns || 80;
  }

  /**
   * Display the status bar
   */
  display(state: StatusBarState): void {
    this.currentState = state;
    
    if (!state.isVisible) {
      this.hide();
      return;
    }

    this.render();
  }

  /**
   * Show thinking status with spinner
   */
  showThinking(message: string = 'Thinking', contextLeft: number = 100): void {
    const spinner: SpinnerState = {
      isActive: true,
      startTime: new Date(),
      frameIndex: 0,
      message
    };

    this.currentState = {
      isVisible: true,
      message,
      controls: 'press Esc twice to interrupt',
      contextInfo: `${contextLeft}% context left`,
      spinner
    };

    // Start spinner animation
    this.spinnerInterval = setInterval(() => {
      if (this.currentState?.spinner) {
        this.currentState.spinner.frameIndex++;
        this.render();
      }
    }, 150);

    this.render();
  }

  /**
   * Show input status
   */
  showInput(contextLeft: number = 100): void {
    this.stopSpinner();
    
    this.currentState = {
      isVisible: true,
      message: '',
      controls: 'ctrl+c to exit | "/" to see commands | enter to send',
      contextInfo: `${contextLeft}% context left`
    };

    this.render();
  }

  /**
   * Hide the status bar
   */
  hide(): void {
    this.stopSpinner();
    this.currentState = null;
    // Clear the status bar line
    process.stdout.write('\r\x1b[K');
  }

  /**
   * Stop spinner animation
   */
  private stopSpinner(): void {
    if (this.spinnerInterval) {
      clearInterval(this.spinnerInterval);
      this.spinnerInterval = null;
    }
  }

  /**
   * Render the status bar
   */
  private render(): void {
    if (!this.currentState || !this.currentState.isVisible) {
      return;
    }

    const terminalWidth = this.getTerminalWidth();
    const boxWidth = Math.min(terminalWidth - 4, 120);

    let statusContent = '';

    // Add spinner and message if present
    if (this.currentState.spinner) {
      const frames = ['●', '○', '◐', '◓', '◑', '◒'];
      const currentFrame = frames[this.currentState.spinner.frameIndex % frames.length];
      const elapsed = Math.floor((Date.now() - this.currentState.spinner.startTime.getTime()) / 1000);
      statusContent = `(${chalk.cyan(currentFrame)}) ${elapsed}s  ${this.currentState.message}`;
    }

    // Calculate spacing
    const controlsText = this.currentState.controls;
    const contextText = this.currentState.contextInfo;

    const contentLength = statusContent.length + controlsText.length + contextText.length;
    const availableSpace = boxWidth - 4 - contentLength; // 4 for borders and padding
    const padding = Math.max(1, availableSpace);

    // Create the status line
    const statusLine = chalk.gray('│') +
                      chalk.white(statusContent) +
                      ' '.repeat(padding) +
                      chalk.gray(controlsText) +
                      ' ' +
                      chalk.white(contextText) +
                      chalk.gray('│');

    // Create borders
    const topBorder = chalk.gray('╭' + '─'.repeat(boxWidth - 2) + '╮');
    const bottomBorder = chalk.gray('╰' + '─'.repeat(boxWidth - 2) + '╯');

    // Clear previous lines and display new status bar
    process.stdout.write('\r\x1b[K\x1b[1A\x1b[K\x1b[1A\x1b[K');
    process.stdout.write(topBorder + '\n' + statusLine + '\n' + bottomBorder);
  }

  /**
   * Get current state
   */
  getCurrentState(): StatusBarState | null {
    return this.currentState;
  }
}
